﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Security.Cryptography;
using ProjetoIntegrador.Coordenador.C_TelaPrincipal;
using ProjetoIntegrador.Classes;
using ProjetoIntegrador.conn;

namespace ProjetoIntegrador.C_Professor
{
    public partial class C_Professor : Form
    {
        public C_Professor()
        {
            InitializeComponent();
            CarregarDadosComboBoxes();
        }

        // Método para gerar hash MD5
        private string GerarMD5(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hashBytes)
                    sb.Append(b.ToString("x2"));
                return sb.ToString();
            }
        }

        // Método para carregar dados nas ComboBoxes
        private void CarregarDadosComboBoxes()
        {
            try
            {
                comandosbanco cmdBanco = new comandosbanco();

                // Carregar disciplinas
                DataTable disciplinas = cmdBanco.ConsultarDisciplinas();
                cb_nomedisciplina_professor.Items.Clear();
                cb_nomedisciplina_professor.Items.Add("Selecione uma disciplina");
                foreach (DataRow row in disciplinas.Rows)
                {
                    cb_nomedisciplina_professor.Items.Add(row["NOME_DISCIPLINA"].ToString());
                }
                cb_nomedisciplina_professor.SelectedIndex = 0;

                // Carregar cursos
                DataTable cursos = cmdBanco.ConsultarCursos();
                cb_nomecurso_professor.Items.Clear();
                cb_nomecurso_professor.Items.Add("Selecione um curso");
                foreach (DataRow row in cursos.Rows)
                {
                    cb_nomecurso_professor.Items.Add(row["NOME_CURSO"].ToString());
                }
                cb_nomecurso_professor.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar dados: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void lbl_senhatext_Click(object sender, EventArgs e)
        {

        }

        private void btn_inserir_Click(object sender, EventArgs e)
        {
            // Validar se todos os campos estão preenchidos
            if (string.IsNullOrWhiteSpace(txt_nomeprofessor_professor.Text) ||
                string.IsNullOrWhiteSpace(txt_RA_professor.Text) ||
                cb_nomedisciplina_professor.SelectedIndex <= 0 ||
                cb_nomecurso_professor.SelectedIndex <= 0)
            {
                MessageBox.Show("Todos os campos precisam ser preenchidos.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Validar se o RA é um número válido
            if (!int.TryParse(txt_RA_professor.Text, out int ra))
            {
                MessageBox.Show("O RA deve ser um número válido.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // Converter a data de nascimento para o formato DDMMAAAA
                DateTime dataNascimento = dtp_datadenascimentoprofessor_professor.Value;
                string dataFormatada = dataNascimento.ToString("ddMMyyyy");

                // Gerar hash MD5 da data de nascimento
                string dataNascimentoMD5 = GerarMD5(dataFormatada);

                // Criar o modelo com os dados do formulário
                variaveisbanco modelo = new variaveisbanco
                {
                    nomeprofessor = txt_nomeprofessor_professor.Text.Trim(),
                    raprofessor = ra,
                    datanascimentoprofessor = dataNascimentoMD5,
                    nomecurso = cb_nomecurso_professor.SelectedItem.ToString()
                };

                // Instanciar a classe de comandos do banco
                comandosbanco cmdBanco = new comandosbanco();

                // Cadastrar o professor
                cmdBanco.CadastroProfessor(modelo);

                MessageBox.Show("Professor cadastrado com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Limpar os campos após o cadastro
                txt_nomeprofessor_professor.Clear();
                txt_RA_professor.Clear();
                cb_nomedisciplina_professor.SelectedIndex = 0;
                cb_nomecurso_professor.SelectedIndex = 0;
                dtp_datadenascimentoprofessor_professor.Value = DateTime.Now;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao cadastrar professor: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_voltar_professor_Click(object sender, EventArgs e)
        {
            // Cria uma nova instância da tela principal e a exibe
            ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide(); // Oculta a tela atual em vez de fechá-la
        }

        private void btn_minimizar_professor_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_professor_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_fechar_professor_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }
    }
}
