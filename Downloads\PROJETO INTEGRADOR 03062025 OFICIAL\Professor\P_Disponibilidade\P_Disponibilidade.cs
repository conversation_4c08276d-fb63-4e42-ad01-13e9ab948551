﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Professor
{
    public partial class P_IDisponibilidade : Form
    {
        public P_IDisponibilidade()
        {
            InitializeComponent();
            CarregarDiasSemana();
        }

        // Método para carregar os dias da semana no ComboBox
        private void CarregarDiasSemana()
        {
            try
            {
                // Limpa o ComboBox
                ComboBox cbDias = Controls.Find("cb_selecionardias", true).FirstOrDefault() as ComboBox;

                if (cbDias != null)
                {
                    cbDias.Items.Clear();

                    // Adiciona um item padrão
                    cbDias.Items.Add("Selecione um dia...");

                    // Adiciona os dias da semana
                    cbDias.Items.Add("Segunda-feira");
                    cbDias.Items.Add("Terça-feira");
                    cbDias.Items.Add("Quarta-feira");
                    cbDias.Items.Add("Quinta-feira");
                    cbDias.Items.Add("Sexta-feira");
                    cbDias.Items.Add("Sábado");

                    // Seleciona o item padrão
                    cbDias.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar dias da semana: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_fechar_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
            "Deseja realmente fechar?",
            "Confirmação",
            MessageBoxButtons.YesNo,
             MessageBoxIcon.Question
);

            if (resultado == DialogResult.Yes)
            {
                this.Close();
            }
        }

        private void btn_minimizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_inserir_Click(object sender, EventArgs e)
        {
            // Obtém os controles do formulário
            TextBox txtHorarioInicio = Controls.Find("txt_horarioinicio", true).FirstOrDefault() as TextBox;
            TextBox txtHorarioFim = Controls.Find("txt_horariofim", true).FirstOrDefault() as TextBox;
            ComboBox cbDias = Controls.Find("cb_selecionardias", true).FirstOrDefault() as ComboBox;

            // Valida se os campos estão preenchidos
            if (txtHorarioInicio != null && txtHorarioFim != null && cbDias != null &&
                !string.IsNullOrWhiteSpace(txtHorarioInicio.Text) &&
                !string.IsNullOrWhiteSpace(txtHorarioFim.Text) &&
                cbDias.SelectedIndex > 0 && // Evita o índice 0 (placeholder)
                TimeOnly.TryParse(txtHorarioInicio.Text, out TimeOnly horarioInicio) &&
                TimeOnly.TryParse(txtHorarioFim.Text, out TimeOnly horarioFim))
            {
                try
                {
                    // Cria o modelo e preenche com dados do formulário
                    variaveisbanco modelo = new variaveisbanco
                    {
                        horarioinicio = horarioInicio,
                        horariofim = horarioFim,
                        diasemana = cbDias.SelectedItem.ToString()
                    };

                    // Instancia a classe de comandos do banco
                    comandosbanco cmdBanco = new comandosbanco();

                    // Cadastra a disponibilidade
                    cmdBanco.CadastroDisponibilidade(modelo);

                    MessageBox.Show("Disponibilidade cadastrada com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Limpa os campos após o cadastro
                    txtHorarioInicio.Clear();
                    txtHorarioFim.Clear();
                    cbDias.SelectedIndex = 0;
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro ao cadastrar disponibilidade: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("Preencha todos os campos obrigatórios corretamente. Os horários devem estar no formato HH:MM.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void P_IDisponibilidade_Load(object sender, EventArgs e)
        {

        }

    }
}
