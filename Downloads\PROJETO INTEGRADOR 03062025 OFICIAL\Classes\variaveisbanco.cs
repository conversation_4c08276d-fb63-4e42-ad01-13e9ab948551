﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Org.BouncyCastle.Asn1.Cms;


namespace ProjetoIntegrador.Classes
{
    internal class variaveisbanco
    {

            // CADASTRO DISCIPLINA

            public string nomedisciplina { get; set; }

            public int cargahoraria { get; set; }


            // CADASTRO COORDENADOR
            public int racoordenador { get; set; }
            public string nomecoordenador { get; set; }
            public int datanascimentocoordenador { get; set; }

            // CADASTRO PROFESSOR

            public int raprofessor { get; set; }
            public string nomeprofessor { get; set; }
            public string datanascimentoprofessor { get; set; }

        // CADASTRO CURSO


            public int ID_CURSO { get; set; }

        public string nomecurso { get; set; }
            public string quantidadeaulas { get; set; }
            public DateOnly inicioperiodo { get; set; }
            public DateOnly fimperiodo { get; set; }

            // CADASTRO DE DISPONIBILIADE

            public TimeOnly horarioinicio { get; set; }
            public TimeOnly horariofim { get; set; }
            public string diasemana { get; set; }

            // CADASTRO DE MATRIZ CURRICULAR
            public int semestre { get; set; }
            public int horaano { get; set; }
    }
}
