﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ProjetoIntegrador.Professor.P_TelaPrincipal
{
    public partial class P_TelaPrincipal : Form
    {
        public P_TelaPrincipal()
        {
            InitializeComponent();
        }

        private void P_TelaPrincipal_Load(object sender, EventArgs e)
        {

        }

        private void AbrirTela(Form novaTela)
        {
            bool estaMaximizado = this.WindowState == FormWindowState.Maximized;

            this.Hide();
            if (estaMaximizado)
                novaTela.WindowState = FormWindowState.Maximized;

            novaTela.FormClosed += (s, args) =>
            {
                this.Show();
                if (estaMaximizado)
                    this.WindowState = FormWindowState.Maximized;
            };


        }

        private void btn_fechar_professor_<PERSON>lick(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
             "Deseja realmente fechar a aplicação?",
             "Confirmação",
             MessageBoxButtons.YesNo,
             MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        private void btn_maximizar_professor_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_minimizar_professor_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_disponibilidade_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Professor.P_IDisponibilidade());

        }

        private void btn_visualizardisponibilidade_Click(object sender, EventArgs e)
        {

            AbrirTela(new ProjetoIntegrador.Professor.P_Disponibilidade.P_VisualizarDisponibilidade());

        }

        private void btn_conferirgrade_Click(object sender, EventArgs e)
        {

        }
    }
}
