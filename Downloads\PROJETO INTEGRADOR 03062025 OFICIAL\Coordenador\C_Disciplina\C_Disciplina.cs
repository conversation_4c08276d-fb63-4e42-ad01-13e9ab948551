﻿using System;
using System.Drawing;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;
using ProjetoIntegrador.Coordenador.C_TelaPrincipal;
using MySql.Data.MySqlClient;

namespace ProjetoIntegrador.C_Disciplina
{
    public partial class C_IDisciplina : Form
    {
        public C_IDisciplina()
        {
            InitializeComponent();
        }

        private void C_IDisciplina_Load(object sender, EventArgs e)
        {
            CarregarCursos();
            CarregarQuantidadeAulas();
        }

        private void CarregarCursos()
        {
            try
            {
                comandosbanco cmdBanco = new comandosbanco();
                var cursos = cmdBanco.ConsultarCursos();

                // Configurar ComboBox de cursos
                cb_selecionarcurso_disciplina.DataSource = cursos;
                cb_selecionarcurso_disciplina.DisplayMember = "NOME_CURSO";  // o que aparece
                cb_selecionarcurso_disciplina.ValueMember = "ID_CURSO";      // valor interno
                cb_selecionarcurso_disciplina.SelectedIndex = -1;            // nenhum selecionado inicialmente
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar cursos: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CarregarQuantidadeAulas()
        {
            // Adicionar opções fixas de quantidade de aulas (1 a 5)
            cb_quantidadeaulas_disciplina.Items.Clear();
            cb_quantidadeaulas_disciplina.Items.Add("1");
            cb_quantidadeaulas_disciplina.Items.Add("2");
            cb_quantidadeaulas_disciplina.Items.Add("3");
            cb_quantidadeaulas_disciplina.Items.Add("4");
            cb_quantidadeaulas_disciplina.Items.Add("5");

            cb_quantidadeaulas_disciplina.SelectedIndex = -1; // nenhum selecionado inicialmente
        }

        // Quando muda seleção no combo, altera a cor para indicar seleção válida
        private void cb_selecionarcurso_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cb_selecionarcurso_disciplina.SelectedIndex == -1)
                cb_selecionarcurso_disciplina.ForeColor = Color.Gray; // Nenhum selecionado
            else
                cb_selecionarcurso_disciplina.ForeColor = Color.Black; // Opção válida
        }


        private void btn_inserir_Click(object sender, EventArgs e)
        {
            // Valida se os campos estão preenchidos e com valores válidos
            if (!string.IsNullOrWhiteSpace(txt_nomedisciplina_disciplina.Text) &&
                cb_selecionarcurso_disciplina.SelectedIndex != -1 &&
                cb_quantidadeaulas_disciplina.SelectedIndex != -1 &&
                !string.IsNullOrWhiteSpace(txt_cargahoraria_disciplina.Text) &&
                int.TryParse(txt_cargahoraria_disciplina.Text, out int cargaHoraria))  // valida se é número inteiro
            {
                // Cria o modelo e preenche com dados do formulário
                variaveisbanco modelo = new variaveisbanco
                {
                    nomedisciplina = txt_nomedisciplina_disciplina.Text,
                    cargahoraria = cargaHoraria,
                    nomecurso = cb_selecionarcurso_disciplina.Text,  // Nome do curso selecionado
                    quantidadeaulas = cb_quantidadeaulas_disciplina.Text  // Quantidade de aulas semanais
                };

                try
                {
                    // Validar se o curso existe
                    if (cb_selecionarcurso_disciplina.SelectedIndex == -1)
                    {
                        MessageBox.Show("Por favor, selecione um curso válido.", "Validação", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    comandosbanco cmdBanco = new comandosbanco();
                    cmdBanco.CadastroDisciplinaSimplificado(modelo);

                    MessageBox.Show($"Disciplina '{modelo.nomedisciplina}' cadastrada com sucesso!\n" +
                                  $"Curso: {modelo.nomecurso}\n" +
                                  $"Carga Horária: {modelo.cargahoraria}h\n" +
                                  $"Aulas Semanais: {cb_quantidadeaulas_disciplina.Text}",
                                  "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Limpar campos após cadastro bem-sucedido
                    LimparCampos();
                }
                catch (MySqlException sqlEx)
                {
                    string mensagemErro = "Erro no banco de dados: ";
                    if (sqlEx.Message.Contains("Duplicate entry"))
                        mensagemErro += "Já existe uma disciplina com este nome ou ID.";
                    else if (sqlEx.Message.Contains("foreign key"))
                        mensagemErro += "Curso selecionado não é válido.";
                    else
                        mensagemErro += sqlEx.Message;

                    MessageBox.Show(mensagemErro, "Erro de Banco de Dados", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Erro inesperado ao cadastrar disciplina:\n{ex.Message}\n\nDetalhes técnicos:\n{ex.StackTrace}",
                                  "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("Preencha todos os campos obrigatórios corretamente.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void LimparCampos()
        {
            txt_nomedisciplina_disciplina.Text = "";
            txt_cargahoraria_disciplina.Text = "";
            cb_selecionarcurso_disciplina.SelectedIndex = -1;
            cb_quantidadeaulas_disciplina.SelectedIndex = -1;
        }

        private void btn_fechar_disciplina_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto
  
         }
        


        private void btn_maximizar_disciplina_Click_1(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_minimizar_disciplina_Click_1(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_voltar_disciplina_Click_1(object sender, EventArgs e)
        {
            C_TelaPrincipal telaPrincipal = new C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide();
        }
    }
}
