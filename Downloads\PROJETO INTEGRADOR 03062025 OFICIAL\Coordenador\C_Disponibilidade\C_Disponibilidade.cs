﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ProjetoIntegrador.Coordenador.C_Disponibilidade
{
    public partial class C_Disponibilidade : Form
    {
        public C_Disponibilidade()
        {
            InitializeComponent();
        }

        private void btn_disciplinas_Click(object sender, EventArgs e)
        {

        }

        private void pnl_lado_C1_Paint(object sender, PaintEventArgs e)
        {

        }

        private void btn_voltar_disponibilidade_Click(object sender, EventArgs e)
        {
            // Cria uma nova instância da tela principal e a exibe
            ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide(); // Oculta a tela atual em vez de fechá-la
        }

        private void btn_minimizar_disponibilidade_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_disponibilidade_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_fechar_disponibilidade_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        private void btn_salvar_disponibilidade_disponibilidade_Click(object sender, EventArgs e)
        {

        }

        private void btn_editar_disponibilidade_disponibilidade_Click(object sender, EventArgs e)
        {

        }

        private void pnl_header_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
