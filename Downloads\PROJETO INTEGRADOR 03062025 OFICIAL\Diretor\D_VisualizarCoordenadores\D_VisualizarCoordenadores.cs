﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;

namespace ProjetoIntegrador.Diretor.D_VisualizarCoordenadores
{
    public partial class D_VisualizarCoordenadores : Form
    {
        private comandosbanco cmdBanco = new comandosbanco();
        private DataTable dadosCoordenadores;
        private int paginaAtual = 0;
        private int registrosPorPagina = 9;
        private int totalPaginas = 0;
        private List<CheckBox> checkBoxes = new List<CheckBox>();

        public D_VisualizarCoordenadores()
        {
            // Comentando InitializeComponent() temporariamente até criarmos o Designer
            // InitializeComponent();
            InicializarComponentes();
            CarregarCoordenadores();
        }

        private void InicializarComponentes()
        {
            // Método temporário para inicializar componentes básicos
            // Este método será substituído quando criarmos o arquivo Designer.cs
            this.Text = "Visualizar Coordenadores";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private void CarregarCoordenadores()
        {
            try
            {
                dadosCoordenadores = cmdBanco.ConsultarCoordenadores();
                totalPaginas = (int)Math.Ceiling((double)dadosCoordenadores.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;

                // ExibirDadosPagina(); // Será implementado quando tivermos o Designer
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar coordenadores: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        //codigo para ser remontado com base de visualizar coordenadores
        private void btn_fechar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
            "Deseja realmente fechar a aplicação?",
            "Confirmação",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        private void btn_minimizar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void lbl_nomecurso_Click(object sender, EventArgs e)
        {

        }

        private void btn_excluir_visualizarcoordenadores_Click(object sender, EventArgs e)
        {
            // Verificar se há checkboxes selecionados
            var checkboxesSelecionados = checkBoxes.Where(cb => cb.Visible && cb.Checked).ToList();

            if (checkboxesSelecionados.Count == 0)
            {
                MessageBox.Show("Selecione pelo menos um coordenador para excluir.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Obter nomes dos coordenadores selecionados para confirmação
            var nomesCoordenadores = checkboxesSelecionados
                .Select(cb => ((DataRow)cb.Tag)["NOME_COORDENADOR"].ToString())
                .ToList();

            string mensagemConfirmacao;
            if (nomesCoordenadores.Count == 1)
            {
                mensagemConfirmacao = $"Deseja realmente excluir o coordenador '{nomesCoordenadores[0]}'?";
            }
            else
            {
                mensagemConfirmacao = $"Deseja realmente excluir {nomesCoordenadores.Count} coordenadores selecionados?\n\n" +
                                    string.Join("\n", nomesCoordenadores.Take(5)) +
                                    (nomesCoordenadores.Count > 5 ? "\n..." : "");
            }

            // Confirmar exclusão
            DialogResult resultado = MessageBox.Show(
                mensagemConfirmacao,
                "Confirmar Exclusão",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                try
                {
                    int coordenadoresExcluidos = 0;
                    var erros = new List<string>();

                    foreach (var checkbox in checkboxesSelecionados)
                    {
                        DataRow row = (DataRow)checkbox.Tag;
                        int idCoordenador = Convert.ToInt32(row["ID_COORDENADOR"]);
                        string nomeCoordenador = row["NOME_COORDENADOR"].ToString();

                        try
                        {
                            cmdBanco.DeletarCoordenador(idCoordenador);
                            coordenadoresExcluidos++;
                        }
                        catch (Exception ex)
                        {
                            erros.Add($"Erro ao excluir '{nomeCoordenador}': {ex.Message}");
                        }
                    }

                    // Exibir resultado
                    if (erros.Count == 0)
                    {
                        string mensagemSucesso = coordenadoresExcluidos == 1
                            ? "Coordenador excluído com sucesso!"
                            : $"{coordenadoresExcluidos} coordenadores excluídos com sucesso!";

                        MessageBox.Show(mensagemSucesso, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string mensagemErro = $"{coordenadoresExcluidos} coordenadores excluídos com sucesso.\n\n" +
                                            $"Erros encontrados:\n{string.Join("\n", erros)}";
                        MessageBox.Show(mensagemErro, "Resultado da Exclusão", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }

                    // Recarregar dados
                    CarregarCoordenadores();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro inesperado ao excluir coordenadores: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
