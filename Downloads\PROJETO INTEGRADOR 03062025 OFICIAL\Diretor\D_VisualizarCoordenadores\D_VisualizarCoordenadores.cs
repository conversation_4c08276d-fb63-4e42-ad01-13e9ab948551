﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;

namespace ProjetoIntegrador.Diretor.D_VisualizarCoordenadores
{
    public partial class D_VisualizarCoordenadores : Form
    {
        public D_VisualizarCoordenadores()
        {
            InitializeComponent();
        }



        //codigo para ser remontado com base de visualizar coordenadores
        private void btn_fechar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
            "Deseja realmente fechar a aplicação?",
            "Confirmação",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        private void btn_minimizar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void lbl_nomecurso_Click(object sender, EventArgs e)
        {

        }

        private void btn_excluir_visualizarcoordenadores_Click(object sender, EventArgs e)
        {
            // Verificar se há checkboxes selecionados
            var checkboxesSelecionados = checkBoxes.Where(cb => cb.Visible && cb.Checked).ToList();

            if (checkboxesSelecionados.Count == 0)
            {
                MessageBox.Show("Selecione pelo menos uma disciplina para excluir.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Obter nomes das disciplinas selecionadas para confirmação
            var nomesDisciplinas = checkboxesSelecionados
                .Select(cb => ((DataRow)cb.Tag)["NOME_DISCIPLINA"].ToString())
                .ToList();

            string mensagemConfirmacao;
            if (nomesDisciplinas.Count == 1)
            {
                mensagemConfirmacao = $"Deseja realmente excluir a disciplina '{nomesDisciplinas[0]}'?";
            }
            else
            {
                mensagemConfirmacao = $"Deseja realmente excluir {nomesDisciplinas.Count} disciplinas selecionadas?\n\n" +
                                    string.Join("\n", nomesDisciplinas.Take(5)) +
                                    (nomesDisciplinas.Count > 5 ? "\n..." : "");
            }

            // Confirmar exclusão
            DialogResult resultado = MessageBox.Show(
                mensagemConfirmacao,
                "Confirmar Exclusão",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                try
                {
                    int disciplinasExcluidas = 0;
                    var erros = new List<string>();

                    foreach (var checkbox in checkboxesSelecionados)
                    {
                        DataRow row = (DataRow)checkbox.Tag;
                        int idDisciplina = Convert.ToInt32(row["ID_DISCIPLINA"]);
                        string nomeDisciplina = row["NOME_DISCIPLINA"].ToString();

                        try
                        {
                            cmdBanco.DeletarDisciplina(idDisciplina);
                            disciplinasExcluidas++;
                        }
                        catch (Exception ex)
                        {
                            erros.Add($"Erro ao excluir '{nomeDisciplina}': {ex.Message}");
                        }
                    }

                    // Exibir resultado
                    if (erros.Count == 0)
                    {
                        string mensagemSucesso = disciplinasExcluidas == 1
                            ? "Disciplina excluída com sucesso!"
                            : $"{disciplinasExcluidas} disciplinas excluídas com sucesso!";

                        MessageBox.Show(mensagemSucesso, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string mensagemErro = $"{disciplinasExcluidas} disciplinas excluídas com sucesso.\n\n" +
                                            $"Erros encontrados:\n{string.Join("\n", erros)}";
                        MessageBox.Show(mensagemErro, "Resultado da Exclusão", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }

                    // Recarregar dados
                    CarregarDisciplinas();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro inesperado ao excluir disciplinas: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
    }
}
