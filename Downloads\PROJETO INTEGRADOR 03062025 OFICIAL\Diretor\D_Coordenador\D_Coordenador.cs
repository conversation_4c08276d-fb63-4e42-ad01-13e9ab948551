﻿using ProjetoIntegrador.Classes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ProjetoIntegrador.Diretor
{
    public partial class D_Coordenador : Form
    {
        public D_Coordenador()
        {
            InitializeComponent();

        }

        // Método para carregar dados nas ComboBoxes
        private string GerarMD5(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hashBytes)
                    sb.Append(b.ToString("x2"));
                return sb.ToString();
            }
        }

        // Método para carregar dados nas ComboBoxes
        private void CarregarDadosComboBoxes()
        {
            try
            {
                comandosbanco cmdBanco = new comandosbanco();
                // Carregar cursos
                DataTable cursos = cmdBanco.ConsultarCursos();
                cb_selecionarcurso_coordenador.Items.Clear();
                cb_selecionarcurso_coordenador.Items.Add("Selecione um curso");
                foreach (DataRow row in cursos.Rows)
                {
                    cb_selecionarcurso_coordenador.Items.Add(row["NOME_CURSO"].ToString());
                }
                cb_selecionarcurso_coordenador.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar dados: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_maximizar_coordenador_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_minimizar_coordenador_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_fechar_coordenador_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (resultado == DialogResult.Yes)
            {
                this.Close();
            }
            // Se for "No", nada acontece e o formulário continua aberto.
        }



        // Método para limpar os campos do formulário
        private void LimparCampos()
        {

        }

        private void btn_voltar_coordenador_Click(object sender, EventArgs e)
        {
            // Cria uma nova instância da tela principal e a exibe
            ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide(); // Oculta a tela atual em vez de fechá-la

        }

        private void btn_inserir_coordenador_Click(object sender, EventArgs e)
        {

        }
    }
}
